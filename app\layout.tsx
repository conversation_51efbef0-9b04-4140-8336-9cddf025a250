import type React from "react"
import "./globals.css"
import { <PERSON><PERSON><PERSON> } from "next/font/google"

const geist = Geist({ subsets: ["latin"] })

export const metadata = {
  title: "AG3NT",
  description: "AI-powered project planning and context generation",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={geist.className}>{children}</body>
    </html>
  )
}
