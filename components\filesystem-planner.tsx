"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FolderTree, Folder, File } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface FileSystemNode {
  name: string
  type: "folder" | "file"
  children?: FileSystemNode[]
  description?: string
}

export function FilesystemPlanner({ context, onComplete }: ModuleProps) {
  const [filesystem, setFilesystem] = useState<FileSystemNode[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateFilesystem()
  }, [])

  const generateFilesystem = async () => {
    setIsGenerating(true)

    // Simulate AI generation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const generatedFilesystem = createFilesystem()
    setFilesystem(generatedFilesystem)
    setIsGenerating(false)
  }

  const createFilesystem = (): FileSystemNode[] => {
    const { techStack, clarifications } = context || {}
    const isReact = techStack?.Frontend === "React" || techStack?.Frontend === "Next.js"
    const isNode = techStack?.Backend === "Node.js"
    const needsAuth = clarifications?.authentication?.includes("Yes")

    const structure: FileSystemNode[] = [
      {
        name: "project-root",
        type: "folder",
        children: [
          {
            name: "README.md",
            type: "file",
            description: "Project documentation and setup instructions",
          },
          {
            name: "package.json",
            type: "file",
            description: "Node.js dependencies and scripts",
          },
          {
            name: ".env.example",
            type: "file",
            description: "Environment variables template",
          },
          {
            name: ".gitignore",
            type: "file",
            description: "Git ignore rules",
          },
        ],
      },
    ]

    if (isReact) {
      structure[0].children?.push({
        name: "src",
        type: "folder",
        children: [
          {
            name: "components",
            type: "folder",
            children: [
              { name: "Header.jsx", type: "file", description: "Main header component" },
              { name: "Footer.jsx", type: "file", description: "Footer component" },
              { name: "Layout.jsx", type: "file", description: "Main layout wrapper" },
            ],
          },
          {
            name: "pages",
            type: "folder",
            children: [
              { name: "Home.jsx", type: "file", description: "Homepage component" },
              { name: "About.jsx", type: "file", description: "About page component" },
            ],
          },
          {
            name: "hooks",
            type: "folder",
            children: [{ name: "useApi.js", type: "file", description: "Custom API hook" }],
          },
          {
            name: "utils",
            type: "folder",
            children: [
              { name: "helpers.js", type: "file", description: "Utility functions" },
              { name: "constants.js", type: "file", description: "Application constants" },
            ],
          },
          {
            name: "styles",
            type: "folder",
            children: [
              { name: "globals.css", type: "file", description: "Global styles" },
              { name: "components.css", type: "file", description: "Component-specific styles" },
            ],
          },
          { name: "App.jsx", type: "file", description: "Main application component" },
          { name: "index.js", type: "file", description: "Application entry point" },
        ],
      })

      if (needsAuth) {
        structure[0].children
          ?.find((child) => child.name === "src")
          ?.children?.push({
            name: "auth",
            type: "folder",
            children: [
              { name: "Login.jsx", type: "file", description: "Login component" },
              { name: "Register.jsx", type: "file", description: "Registration component" },
              { name: "AuthContext.js", type: "file", description: "Authentication context" },
            ],
          })
      }
    }

    if (isNode) {
      structure[0].children?.push({
        name: "server",
        type: "folder",
        children: [
          { name: "index.js", type: "file", description: "Server entry point" },
          {
            name: "routes",
            type: "folder",
            children: [
              { name: "api.js", type: "file" as const, description: "API routes" },
              ...(needsAuth ? [{ name: "auth.js", type: "file" as const, description: "Authentication routes" }] : []),
            ],
          },
          {
            name: "middleware",
            type: "folder",
            children: [
              { name: "cors.js", type: "file" as const, description: "CORS middleware" },
              ...(needsAuth ? [{ name: "auth.js", type: "file" as const, description: "Auth middleware" }] : []),
            ],
          },
          {
            name: "models",
            type: "folder",
            children: [
              { name: "User.js", type: "file", description: "User data model" },
              { name: "index.js", type: "file", description: "Model exports" },
            ],
          },
          {
            name: "config",
            type: "folder",
            children: [
              { name: "database.js", type: "file", description: "Database configuration" },
              { name: "server.js", type: "file", description: "Server configuration" },
            ],
          },
        ],
      })
    }

    structure[0].children?.push({
      name: "public",
      type: "folder",
      children: [
        { name: "index.html", type: "file", description: "Main HTML template" },
        { name: "favicon.ico", type: "file", description: "Site favicon" },
        {
          name: "assets",
          type: "folder",
          children: [
            { name: "images", type: "folder", description: "Image assets" },
            { name: "icons", type: "folder", description: "Icon assets" },
          ],
        },
      ],
    })

    structure[0].children?.push({
      name: "tests",
      type: "folder",
      children: [
        { name: "unit", type: "folder", description: "Unit tests" },
        { name: "integration", type: "folder", description: "Integration tests" },
        { name: "setup.js", type: "file", description: "Test setup configuration" },
      ],
    })

    return structure
  }

  const renderFileTree = (nodes: FileSystemNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={index} className="text-sm">
        <div className={`flex items-center gap-2 py-1 ${depth > 0 ? `ml-${depth * 4}` : ""}`}>
          {node.type === "folder" ? (
            <Folder className="w-4 h-4 text-blue-400" />
          ) : (
            <File className="w-4 h-4 text-gray-400" />
          )}
          <span className="text-gray-300 font-mono">{node.name}</span>
          {node.description && <span className="text-xs text-gray-500 ml-2">- {node.description}</span>}
        </div>
        {node.children && renderFileTree(node.children, depth + 1)}
      </div>
    ))
  }

  const handleContinue = () => {
    onComplete(filesystem)
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <FolderTree className="w-5 h-5 text-purple-400" />
          <CardTitle className="text-white">Project File Structure</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <FolderTree className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Planning project file structure...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-slate-900 p-4 rounded-lg max-h-96 overflow-y-auto">{renderFileTree(filesystem)}</div>

            <Button onClick={handleContinue} className="w-full mt-6 bg-purple-600 hover:bg-purple-700">
              Approve File Structure & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
