export interface ContextEngineConfig {
  ragEnabled: boolean
  memoryDepth: number
  validationLevel: "basic" | "strict" | "paranoid"
  templateVersion: string
}

export interface EnhancedProjectContext {
  // Core context
  originalPrompt: string
  projectType: string
  features: string[]

  // Context engineering metadata
  complexity: "Simple" | "Medium" | "Complex"
  confidence: number
  completeness: number

  // RAG-enhanced data
  externalKnowledge: Record<string, any>
  bestPractices: string[]
  templates: Record<string, any>

  // Memory and dependencies
  stepHistory: string[]
  dependencies: Record<string, any>

  // Validation and security
  validation: {
    isValid: boolean
    issues: string[]
    securityScore: number
  }
}

export interface PlanningStep {
  id: string
  name: string
  dependencies: string[]
  templates: any[]
  ragQueries: string[]
  validationRules: string[]
}
