import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Design agent: Processing image upload request')

    const formData = await request.formData()
    const images = formData.getAll('images') as File[]

    if (!images || images.length === 0) {
      console.log('Design agent: No images provided')
      return NextResponse.json({ error: 'No images provided' }, { status: 400 })
    }

    console.log(`Design agent: Processing ${images.length} images`)

    // Validate images
    for (const image of images) {
      if (!image.type.startsWith('image/')) {
        console.log(`Design agent: Invalid file type: ${image.type}`)
        return NextResponse.json({ error: `Invalid file type: ${image.type}` }, { status: 400 })
      }
      if (image.size > 10 * 1024 * 1024) {
        console.log(`Design agent: File too large: ${image.size} bytes`)
        return NextResponse.json({ error: `File ${image.name} is too large (max 10MB)` }, { status: 400 })
      }
    }

    // Convert images to base64
    console.log('Design agent: Converting images to base64')
    const imagePromises = images.map(async (image) => {
      const bytes = await image.arrayBuffer()
      const base64 = Buffer.from(bytes).toString('base64')
      return {
        type: 'image_url',
        image_url: {
          url: `data:${image.type};base64,${base64}`
        }
      }
    })

    const imageContent = await Promise.all(imagePromises)
    console.log('Design agent: Images converted successfully')

    // Create the prompt for style guide generation
    const prompt = `You are a senior UI/UX designer and design systems expert. Analyze the provided design reference images with extreme attention to detail and create a comprehensive, professional web design style guide that matches the quality and depth of enterprise design systems.

CRITICAL: Extract EXACT colors, measurements, and styling details from the images. Do not make assumptions or use generic values.

Your response must be a complete, detailed style guide document following this EXACT structure:

# [Project Name]: Web Design Style Guide

This document outlines the design standards and visual components for the [Project Name] platform. Adhering to this guide will ensure a cohesive, intuitive, and premium user experience.

## 1. Color Palette
The color scheme is [describe the overall theme - dark/light, modern/classic, etc.], using [describe accent strategy].

Create a detailed color table with EXACT hex codes extracted from the image:

| Swatch | Role | HEX | RGB | Usage Context |
|--------|------|-----|-----|---------------|
| [color swatch] | Primary Background | #XXXXXX | R, G, B | Main page background, primary containers |
| [color swatch] | Secondary Background | #XXXXXX | R, G, B | Secondary containers, sidebar backgrounds |
| [color swatch] | Card/Module Background | #XXXXXX | R, G, B | Content cards, modal backgrounds |
| [color swatch] | Primary Accent | #XXXXXX | R, G, B | Call-to-action buttons, links, highlights |
| [color swatch] | Secondary Accent | #XXXXXX | R, G, B | Secondary actions, hover states |
| [color swatch] | Primary Text | #XXXXXX | R, G, B | Headings, primary content |
| [color swatch] | Secondary Text | #XXXXXX | R, G, B | Body text, descriptions |
| [color swatch] | Muted Text | #XXXXXX | R, G, B | Labels, metadata, placeholders |
| [color swatch] | Borders/Dividers | #XXXXXX | R, G, B | Component borders, section dividers |
| [color swatch] | Success | #XXXXXX | R, G, B | Success states, positive feedback |
| [color swatch] | Warning | #XXXXXX | R, G, B | Warning states, caution indicators |
| [color swatch] | Error | #XXXXXX | R, G, B | Error states, destructive actions |

## 2. Typography
The typography is [describe style - clean, modern, serif, etc.], using [font family or similar fonts].

**Font Family:** [Exact font name] (or similar: [alternatives])

### Typographic Scale
| Element | Font Weight | Font Size | Line Height | Letter Spacing | Use Case |
|---------|-------------|-----------|-------------|----------------|----------|
| Heading 1 (Hero) | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Heading 2 | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Heading 3 | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Heading 4 | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Body (Large) | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Body (Standard) | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Body (Small) | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Label/Meta | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Button Text | [weight] | [size]px | [height]px | [spacing] | [usage] |
| Caption | [weight] | [size]px | [height]px | [spacing] | [usage] |

## 3. Layout & Spacing
The layout is based on [describe grid system] with [describe spacing philosophy].

- **Grid:** [X]-column responsive grid
- **Gutter Width:** [X]px
- **Max Container Width:** [X]px
- **Base Spacing Unit:** [X]px. All padding and margins should be multiples of this unit ([list common values])
- **Breakpoints:** Mobile: [X]px, Tablet: [X]px, Desktop: [X]px

## 4. Components

### Buttons
Buttons are used for [describe button philosophy and usage].

#### Primary Button (CTA)
- **Background:** [Color] ([hex])
- **Text Color:** [Color] ([hex])
- **Padding:** [X]px [Y]px
- **Border Radius:** [X]px
- **Font:** [specifications]
- **Icon:** [describe icon usage]
- **Hover State:** [describe hover behavior]
- **Focus State:** [describe focus behavior]
- **Disabled State:** [describe disabled behavior]

#### Secondary Button
- **Background:** [specifications]
- **Text Color:** [specifications]
- **Border:** [specifications]
- **Padding:** [specifications]
- **Border Radius:** [specifications]
- **Hover State:** [describe behavior]

#### Icon Button
- **Background:** [specifications]
- **Icon Color:** [specifications]
- **Size:** [X]px × [Y]px
- **Border Radius:** [specifications]
- **Hover State:** [describe behavior]

### Cards
Cards are used to [describe card usage and philosophy].

- **Background:** [Color] ([hex])
- **Padding:** [X]px - [Y]px
- **Border Radius:** [X]px
- **Border:** [specifications]
- **Box Shadow:** [specifications or "None"]
- **Hover State:** [describe behavior]

### Navigation
- **Header:** [describe header styling and behavior]
- **Active State:** [specifications]
- **Inactive State:** [specifications]
- **Hover State:** [specifications]

### Form Elements
- **Input Fields:** [detailed specifications]
- **Labels:** [specifications]
- **Placeholders:** [specifications]
- **Focus States:** [specifications]
- **Error States:** [specifications]

### Icons
- **Style:** [describe icon style]
- **Recommended Library:** [suggestions]
- **Size:** [standard sizes]
- **Color:** [color specifications for different states]

## 5. Visual Effects & Interactions
- **Shadows:** [list shadow specifications]
- **Gradients:** [list any gradients used]
- **Transitions:** [describe animation timing and easing]
- **Hover Effects:** [describe interaction patterns]
- **Loading States:** [describe loading indicators]

## 6. Implementation Guidelines
### CSS Custom Properties
\`\`\`css
:root {
  /* Colors */
  --color-primary-bg: [hex];
  --color-secondary-bg: [hex];
  --color-accent: [hex];
  --color-text-primary: [hex];
  --color-text-secondary: [hex];

  /* Typography */
  --font-family-primary: [font];
  --font-size-base: [size];
  --line-height-base: [height];

  /* Spacing */
  --spacing-unit: [size];
  --spacing-xs: [size];
  --spacing-sm: [size];
  --spacing-md: [size];
  --spacing-lg: [size];
  --spacing-xl: [size];

  /* Border Radius */
  --radius-sm: [size];
  --radius-md: [size];
  --radius-lg: [size];
}
\`\`\`

### Responsive Behavior
[Describe how components adapt across screen sizes]

### Accessibility Considerations
[List accessibility features and requirements]

IMPORTANT:
- Extract EXACT colors from the image - use color picker precision
- Measure actual spacing and sizing from the image
- Identify the specific font family or provide accurate alternatives
- Document every visual detail you can observe
- Create a style guide that a developer could use to recreate the design pixel-perfectly
- Use professional design system language and structure
- Be extremely thorough and specific with all measurements and specifications`

    // Call OpenRouter API with Gemini 2.5 Pro
    console.log('Design agent: Calling OpenRouter API with Gemini 2.5 Pro')

    if (!process.env.OPENROUTER_API_KEY) {
      console.error('Design agent: OPENROUTER_API_KEY not configured')
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      )
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Title': 'Planning Agent Design Analysis',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              ...imageContent
            ]
          }
        ],
        max_tokens: 8000,
        temperature: 0.1
      })
    })

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text()
      console.error('Design agent: OpenRouter API error:', openRouterResponse.status, errorText)

      let errorMessage = 'Failed to analyze images with design agent'
      if (openRouterResponse.status === 401) {
        errorMessage = 'Invalid API key'
      } else if (openRouterResponse.status === 429) {
        errorMessage = 'Rate limit exceeded. Please try again later.'
      } else if (openRouterResponse.status === 400) {
        errorMessage = 'Invalid request format'
      }

      return NextResponse.json(
        { error: errorMessage, details: errorText },
        { status: openRouterResponse.status }
      )
    }

    const result = await openRouterResponse.json()
    console.log('Design agent: OpenRouter API response received')

    const styleGuide = result.choices[0]?.message?.content

    if (!styleGuide) {
      console.error('Design agent: No style guide content in response:', result)
      return NextResponse.json(
        { error: 'No style guide generated', details: 'Empty response from AI' },
        { status: 500 }
      )
    }

    console.log(`Design agent: Style guide generated successfully (${styleGuide.length} characters)`)

    return NextResponse.json({
      success: true,
      styleGuide,
      imageCount: images.length,
      processingTime: Date.now()
    })

  } catch (error) {
    console.error('Design agent error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
