import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Design agent: Processing image upload request')

    const formData = await request.formData()
    const images = formData.getAll('images') as File[]

    if (!images || images.length === 0) {
      console.log('Design agent: No images provided')
      return NextResponse.json({ error: 'No images provided' }, { status: 400 })
    }

    console.log(`Design agent: Processing ${images.length} images`)

    // Validate images
    for (const image of images) {
      if (!image.type.startsWith('image/')) {
        console.log(`Design agent: Invalid file type: ${image.type}`)
        return NextResponse.json({ error: `Invalid file type: ${image.type}` }, { status: 400 })
      }
      if (image.size > 10 * 1024 * 1024) {
        console.log(`Design agent: File too large: ${image.size} bytes`)
        return NextResponse.json({ error: `File ${image.name} is too large (max 10MB)` }, { status: 400 })
      }
    }

    // Convert images to base64
    console.log('Design agent: Converting images to base64')
    const imagePromises = images.map(async (image) => {
      const bytes = await image.arrayBuffer()
      const base64 = Buffer.from(bytes).toString('base64')
      return {
        type: 'image_url',
        image_url: {
          url: `data:${image.type};base64,${base64}`
        }
      }
    })

    const imageContent = await Promise.all(imagePromises)
    console.log('Design agent: Images converted successfully')

    // Create the prompt for style guide generation
    const prompt = `Analyze the provided design reference images and create a comprehensive web design style guide. Your response should be detailed and professional, covering all visual aspects that can be extracted from the images.

Please structure your response as a complete style guide document with the following sections:

# [Project Name] Design Style Guide

## 1. Color Palette
Extract and document all colors used, including hex codes and RGB values. Organize by:
- Primary colors (main brand colors)
- Secondary colors (supporting colors)
- Accent colors (call-to-action, highlights)
- Text colors (headings, body, labels)
- Background colors (main, secondary, cards)
- Border/divider colors

For each color, provide:
- Color name/role
- Hex code (e.g., #FF2D55)
- RGB values (e.g., 255, 45, 85)
- Usage context

## 2. Typography
Identify and document:
- Font families (primary and secondary)
- Font weights (light, regular, medium, bold)
- Font sizes for different elements
- Line heights and letter spacing
- Typographic hierarchy (H1, H2, H3, body, labels, etc.)

## 3. Layout & Spacing
Analyze and document:
- Grid system (columns, gutters)
- Spacing patterns (margins, padding)
- Container max-widths
- Breakpoints for responsive design
- Alignment patterns

## 4. Components
Document visible UI components:
- Buttons (primary, secondary, sizes, states)
- Cards (styling, shadows, borders)
- Navigation elements
- Form elements
- Icons and their styling
- Any unique components

## 5. Visual Style
Describe:
- Overall aesthetic (modern, minimal, bold, etc.)
- Visual effects (shadows, gradients, transparency)
- Border radius patterns
- Animation/transition hints
- Design principles evident in the layout

## 6. Implementation Notes
Provide developer-ready specifications:
- CSS custom properties for colors
- Recommended CSS frameworks or approaches
- Key measurements in pixels/rem
- Responsive behavior patterns

Be extremely detailed and specific with measurements, colors, and styling properties. Format the response as a professional style guide document that a developer could use to implement the design accurately. Use markdown formatting for better readability.`

    // Call OpenRouter API with Gemini 2.5 Pro
    console.log('Design agent: Calling OpenRouter API with Gemini 2.5 Pro')

    if (!process.env.OPENROUTER_API_KEY) {
      console.error('Design agent: OPENROUTER_API_KEY not configured')
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      )
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Title': 'Planning Agent Design Analysis',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              ...imageContent
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.3
      })
    })

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text()
      console.error('Design agent: OpenRouter API error:', openRouterResponse.status, errorText)

      let errorMessage = 'Failed to analyze images with design agent'
      if (openRouterResponse.status === 401) {
        errorMessage = 'Invalid API key'
      } else if (openRouterResponse.status === 429) {
        errorMessage = 'Rate limit exceeded. Please try again later.'
      } else if (openRouterResponse.status === 400) {
        errorMessage = 'Invalid request format'
      }

      return NextResponse.json(
        { error: errorMessage, details: errorText },
        { status: openRouterResponse.status }
      )
    }

    const result = await openRouterResponse.json()
    console.log('Design agent: OpenRouter API response received')

    const styleGuide = result.choices[0]?.message?.content

    if (!styleGuide) {
      console.error('Design agent: No style guide content in response:', result)
      return NextResponse.json(
        { error: 'No style guide generated', details: 'Empty response from AI' },
        { status: 500 }
      )
    }

    console.log(`Design agent: Style guide generated successfully (${styleGuide.length} characters)`)

    return NextResponse.json({
      success: true,
      styleGuide,
      imageCount: images.length,
      processingTime: Date.now()
    })

  } catch (error) {
    console.error('Design agent error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
