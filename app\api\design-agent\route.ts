import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Design agent: Processing image upload request')

    const formData = await request.formData()
    const images = formData.getAll('images') as File[]

    if (!images || images.length === 0) {
      console.log('Design agent: No images provided')
      return NextResponse.json({ error: 'No images provided' }, { status: 400 })
    }

    console.log(`Design agent: Processing ${images.length} images`)

    // Validate images
    for (const image of images) {
      if (!image.type.startsWith('image/')) {
        console.log(`Design agent: Invalid file type: ${image.type}`)
        return NextResponse.json({ error: `Invalid file type: ${image.type}` }, { status: 400 })
      }
      if (image.size > 10 * 1024 * 1024) {
        console.log(`Design agent: File too large: ${image.size} bytes`)
        return NextResponse.json({ error: `File ${image.name} is too large (max 10MB)` }, { status: 400 })
      }
    }

    // Convert images to base64
    console.log('Design agent: Converting images to base64')
    const imagePromises = images.map(async (image) => {
      const bytes = await image.arrayBuffer()
      const base64 = Buffer.from(bytes).toString('base64')
      return {
        type: 'image_url',
        image_url: {
          url: `data:${image.type};base64,${base64}`
        }
      }
    })

    const imageContent = await Promise.all(imagePromises)
    console.log('Design agent: Images converted successfully')

    // Create the prompt for style guide generation
    const prompt = `Analyze this design reference image and create a comprehensive web design style guide. Look at the image carefully and extract EXACT colors, typography, spacing, and component details.

IMPORTANT:
- Use a color picker to get EXACT hex codes from the image
- Measure actual spacing and sizing
- Identify specific fonts or provide accurate alternatives
- Document every visual detail you can see
- Create a professional style guide that a developer could use to recreate this design exactly

Your response must be a detailed style guide document following this structure:

# Web Design Style Guide

## 1. Color Palette

Look at the image and identify ALL colors used. Extract the EXACT hex codes for each color you see:

**Primary Background:** #[extract exact hex from darkest background areas]
**Secondary Background:** #[extract hex from secondary background areas]
**Card/Module Background:** #[extract hex from card/content backgrounds]
**Primary Accent:** #[extract hex from main accent color - buttons, highlights]
**Secondary Accent:** #[extract hex from secondary accent colors]
**Primary Text:** #[extract hex from main text color]
**Secondary Text:** #[extract hex from secondary/muted text]
**Border Color:** #[extract hex from borders and dividers]

For each color, also provide RGB values: rgb(R, G, B)

## 2. Typography

Look at the text in the image and identify:

**Font Family:** [What font is being used? If unsure, suggest similar fonts like Poppins, Inter, Roboto, etc.]
**Main Heading Size:** [estimate pixel size of largest headings]
**Body Text Size:** [estimate pixel size of regular text]
**Small Text Size:** [estimate pixel size of labels/metadata]
**Font Weights Used:** [light, regular, medium, bold - which ones do you see?]
**Line Height:** [estimate the line spacing - tight, normal, loose]

## 3. Layout & Spacing

Look at the spacing and layout in the image:

**Container Width:** [estimate the max width of the main content area]
**Spacing Between Elements:** [estimate padding/margins - small, medium, large values]
**Border Radius:** [estimate corner radius on buttons, cards, etc.]
**Grid Columns:** [how many columns does the layout appear to use?]

## 4. Components

Look at the UI components in the image and describe:

**Buttons:**
- Primary button background color: #[hex]
- Primary button text color: #[hex]
- Button border radius: [estimate]px
- Button padding: [estimate]

**Cards/Containers:**
- Card background color: #[hex]
- Card border radius: [estimate]px
- Card padding: [estimate]
- Card border: [describe if any]

**Navigation:**
- Navigation background: #[hex]
- Active link color: #[hex]
- Inactive link color: #[hex]

**Other Elements:**
[Describe any other UI elements you see - forms, icons, etc.]
## 5. Overall Design Notes

**Visual Style:** [Describe the overall aesthetic - modern, minimal, dark, etc.]
**Shadows:** [Are there any drop shadows or box shadows visible?]
**Effects:** [Any gradients, transparency, or special effects?]

## 6. CSS Variables (Ready to Use)

Based on the colors extracted above, here are CSS custom properties:

\`\`\`css
:root {
  --bg-primary: #[primary background hex];
  --bg-secondary: #[secondary background hex];
  --bg-card: #[card background hex];
  --color-accent: #[accent color hex];
  --color-text: #[text color hex];
  --color-text-muted: #[muted text hex];
  --border-color: #[border color hex];
}
\`\`\`

REMEMBER: Look carefully at the image and extract the ACTUAL colors you see. Don't use generic colors - use the exact colors from this specific design.`

    // Call OpenRouter API with Claude 3.5 Sonnet
    console.log('Design agent: Calling OpenRouter API with Claude 3.5 Sonnet')

    if (!process.env.OPENROUTER_API_KEY) {
      console.error('Design agent: OPENROUTER_API_KEY not configured')
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      )
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Title': 'Planning Agent Design Analysis',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          {
            role: 'system',
            content: 'You are an expert UI/UX designer with perfect color vision and precise measurement skills. When analyzing design images, you extract EXACT colors using color picker precision and provide specific measurements. You never use generic or placeholder values.'
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              ...imageContent
            ]
          }
        ],
        max_tokens: 8000,
        temperature: 0.1
      })
    })

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text()
      console.error('Design agent: OpenRouter API error:', openRouterResponse.status, errorText)

      let errorMessage = 'Failed to analyze images with design agent'
      if (openRouterResponse.status === 401) {
        errorMessage = 'Invalid API key'
      } else if (openRouterResponse.status === 429) {
        errorMessage = 'Rate limit exceeded. Please try again later.'
      } else if (openRouterResponse.status === 400) {
        errorMessage = 'Invalid request format'
      }

      return NextResponse.json(
        { error: errorMessage, details: errorText },
        { status: openRouterResponse.status }
      )
    }

    const result = await openRouterResponse.json()
    console.log('Design agent: OpenRouter API response received')

    const styleGuide = result.choices[0]?.message?.content

    if (!styleGuide) {
      console.error('Design agent: No style guide content in response:', result)
      return NextResponse.json(
        { error: 'No style guide generated', details: 'Empty response from AI' },
        { status: 500 }
      )
    }

    console.log(`Design agent: Style guide generated successfully (${styleGuide.length} characters)`)
    console.log('Design agent: First 500 characters of response:', styleGuide.substring(0, 500))

    // Check if the response contains the expected detailed format
    if (!styleGuide.includes('Color Palette') || !styleGuide.includes('Typography') || styleGuide.length < 1000) {
      console.warn('Design agent: Response appears to be too basic or incomplete')
      console.log('Design agent: Full response:', styleGuide)
    }

    return NextResponse.json({
      success: true,
      styleGuide,
      imageCount: images.length,
      processingTime: Date.now()
    })

  } catch (error) {
    console.error('Design agent error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
