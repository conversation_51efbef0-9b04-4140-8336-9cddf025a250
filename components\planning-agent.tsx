"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, Sparkles, MessageCircle, User, Bot, AlertCircle, Paperclip, X, Loader2 } from "lucide-react"
import type { PlanningTask, Question } from "@/types/planning"
import { ResultsView } from "@/components/results-view"
import Image from "next/image"

const PLANNING_TASKS: PlanningTask[] = [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
  { id: "wireframes", title: "Design UI wireframes", completed: false },
  { id: "design", title: "Create design guidelines", completed: false },
  { id: "database", title: "Design database schema", completed: false },
  { id: "filesystem", title: "Plan file structure", completed: false },
  { id: "workflow", title: "Define workflow logic", completed: false },
  { id: "tasks", title: "Break down implementation tasks", completed: false },
  { id: "scaffold", title: "Generate project scaffold", completed: false },
]

export function PlanningAgent() {
  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(PLANNING_TASKS)
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [planningContext, setPlanningContext] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  // Removed isUsingFallback - using AI service only

  const processNextTask = async (context: any) => {
    console.log(`processNextTask called with currentTaskIndex: ${currentTaskIndex}`)

    if (currentTaskIndex < 0 || currentTaskIndex >= tasks.length) {
      console.log("Invalid task index, finishing processing")
      setIsProcessing(false)
      return
    }

    const currentTask = tasks[currentTaskIndex]
    if (!currentTask) {
      console.log("No current task found, finishing processing")
      setIsProcessing(false)
      return
    }

    console.log(`Processing task: ${currentTask.id} (index: ${currentTaskIndex})`)
    console.log(`Context keys:`, Object.keys(context || {}))
    console.log(`Has design style guide:`, !!context.designStyleGuide)

    try {
      const response = await fetch("/api/planning/step", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          step: currentTask.id,
          context: {
            ...context,
            prompt: userPrompt,
            isInteractive: isInteractive,
            userAnswers: userAnswers,
            ...(context.designStyleGuide && { designStyleGuide: context.designStyleGuide }),
            hasImages: uploadedImages.length > 0,
          },
          answer: questionAnswer,
        }),
      })

      // Check if response is ok first
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`API Error ${response.status}:`, errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      // Check content type
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const textResponse = await response.text()
        console.error("Non-JSON response received:", textResponse.substring(0, 200))
        throw new Error("Server returned non-JSON response")
      }

      const responseText = await response.text()
      console.log(`API response for ${currentTask.id}:`, responseText.substring(0, 200) + "...")

      let result
      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error("Failed to parse response as JSON:", parseError)
        console.error("Raw response:", responseText.substring(0, 500))
        throw new Error("Invalid JSON response from API")
      }

      // Validate result structure
      if (!result || typeof result !== "object") {
        throw new Error("Invalid result structure")
      }

      // Check if we need user input
      if (result.needsInput && result.question && isInteractive) {
        console.log("Need user input:", result.question.question)
        setCurrentQuestion(result.question)
        setPlanningContext({ ...context, ...result })
        return
      }

      // Check if there's an error but we have fallback results
      if (result.error && result.results) {
        console.log("Using fallback results due to error:", result.error)
        setError(`Step processing issue: ${result.error}`)
      }

      // Mark task as completed and store results
      setTasks((prev) => prev.map((task, index) => (index === currentTaskIndex ? { ...task, completed: true } : task)))

      // Store the result data
      if (result.results && result.results[currentTask.id]) {
        setResults((prev) => ({ ...prev, [currentTask.id]: result.results[currentTask.id] }))
        console.log(`Stored result for ${currentTask.id}:`, result.results[currentTask.id])
      } else {
        console.warn(`No result data for ${currentTask.id}`)
      }

      setPlanningContext({ ...context, ...result })

      // Move to next task
      if (currentTaskIndex < tasks.length - 1) {
        console.log(`Moving to next task (${currentTaskIndex + 1})`)
        setTimeout(() => {
          setCurrentTaskIndex((prev) => prev + 1)
        }, 1000)
      } else {
        console.log("All tasks completed!")
        setIsProcessing(false)
      }
    } catch (error) {
      console.error("Failed to process step:", error)

      // Enhanced error handling for step processing
      let errorMessage = `AI step processing failed for ${currentTask.title}. `
      let shouldRetry = false

      if (error instanceof Error) {
        if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          shouldRetry = true
        } else if (error.message.includes("timeout")) {
          errorMessage += "Request timed out."
          shouldRetry = true
        } else if (error.message.includes("network")) {
          errorMessage += "Network error occurred."
          shouldRetry = true
        } else {
          errorMessage += error.message
        }
      } else {
        errorMessage += "Unknown error occurred."
      }

      setError(errorMessage + (shouldRetry ? " You can try again." : " Please check your AI service configuration."))
      setCanRetry(shouldRetry)

      // Stop processing on error - no fallback to mock data
      setIsProcessing(false)
    }
  }

  const handleStartPlanning = async () => {
    if (!userPrompt.trim()) return

    console.log("Starting planning process...")
    setHasStarted(true)
    setIsProcessing(true)
    setCurrentTaskIndex(0)
    setError(null)

    try {
      let designStyleGuide = null

      // If images are uploaded, use the design agent first
      if (uploadedImages.length > 0) {
        console.log("Processing uploaded images with design agent...")
        setIsProcessingImages(true)

        try {
          const formData = new FormData()
          uploadedImages.forEach((image, index) => {
            formData.append('images', image)
          })

          const designResponse = await fetch("/api/design-agent", {
            method: "POST",
            body: formData,
          })

          if (designResponse.ok) {
            const designResult = await designResponse.json()
            designStyleGuide = designResult.styleGuide
            console.log("Design agent generated style guide")
          } else {
            const errorText = await designResponse.text()
            console.warn("Design agent failed:", errorText)
            // Continue without style guide but show a warning
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
          // Continue without style guide
        } finally {
          setIsProcessingImages(false)
        }
      }

      // Test the API connection first
      const response = await fetch("/api/planning", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: userPrompt,
          isInteractive,
          answers: userAnswers,
          designStyleGuide, // Include the generated style guide
          hasImages: uploadedImages.length > 0,
        }),
      })

      const responseText = await response.text()
      console.log("Initial API response:", responseText)

      let result
      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error("Failed to parse initial response:", parseError)
        throw new Error("Invalid JSON response from planning API")
      }

      if (!response.ok) {
        console.error("API returned error:", result)
        throw new Error(`API Error: ${response.status} - ${result.error || "Unknown error"}`)
      }

      console.log("Using AI-powered planning mode")
      setPlanningContext(result)
      // Start AI-based processing
      setCurrentTaskIndex(0)
    } catch (error) {
      console.error("Failed to start AI planning:", error)

      // Enhanced error handling with specific error types
      let errorMessage = "AI planning failed. "
      let suggestion = ""

      if (error instanceof Error) {
        if (error.message.includes("401") || error.message.includes("403")) {
          errorMessage += "Authentication failed."
          suggestion = "Please check your OPENROUTER_API_KEY is valid and has sufficient credits."
        } else if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          suggestion = "Please wait a moment and try again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage += "Network connection failed."
          suggestion = "Please check your internet connection and try again."
        } else {
          errorMessage += error.message
          suggestion = "Please check your OPENROUTER_API_KEY and try again."
        }
      } else {
        errorMessage += "Unknown error occurred."
        suggestion = "Please refresh the page and try again."
      }

      setError(`${errorMessage} ${suggestion}`)
      // Do not fallback to mock data - show error instead
      setIsProcessing(false)
    }
  }

  // All mock data generation removed - using AI service only

  // All mock wireframe generation removed

  // All mock helper functions removed - using AI service only

  const retryCurrentStep = () => {
    setError(null)
    setCanRetry(false)
    setIsProcessing(true)

    // Retry the current step
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length) {
      setTimeout(() => processNextTask(planningContext || {}), 500)
    } else {
      // Retry from the beginning
      setTimeout(() => handleStartPlanning(), 500)
    }
  }

  const handleQuestionSubmit = async () => {
    if (currentQuestion) {
      setUserAnswers((prev) => ({ ...prev, [currentQuestion.id]: questionAnswer }))
      setQuestionAnswer("")
      setCurrentQuestion(null)

      // Continue processing with the answer using AI
      setTimeout(() => processNextTask(planningContext), 500)
    }
  }

  const handleQuestionKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleQuestionSubmit()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleStartPlanning()
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const imageFiles = Array.from(files).filter(file => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          return false
        }
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`)
          return false
        }
        return true
      })

      // Limit total number of images to 5
      const currentCount = uploadedImages.length
      const newFiles = imageFiles.slice(0, Math.max(0, 5 - currentCount))

      if (newFiles.length < imageFiles.length) {
        alert(`Maximum 5 images allowed. Only the first ${newFiles.length} images were added.`)
      }

      setUploadedImages(prev => [...prev, ...newFiles])
    }
    // Reset the input value to allow uploading the same file again
    e.target.value = ''
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const triggerImageUpload = () => {
    fileInputRef.current?.click()
  }

  const clearAllImages = () => {
    setUploadedImages([])
  }

  useEffect(() => {
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length && isProcessing && !currentQuestion) {
      console.log(`useEffect triggered for task index: ${currentTaskIndex}`)
      processNextTask(planningContext || {})
    }
  }, [currentTaskIndex, isProcessing, currentQuestion])

  const openResults = () => {
    setShowResults(true)
  }

  const backToPlanning = () => {
    setShowResults(false)
  }

  if (showResults) {
    return <ResultsView results={results} userPrompt={userPrompt} onBack={backToPlanning} />
  }

  // Question overlay (only shown in interactive mode)
  if (currentQuestion && isInteractive) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <MessageCircle className="w-5 h-5 text-red-400" />
              <span className="text-sm text-gray-400">AG3NT is asking</span>
            </div>

            <h2 className="text-2xl font-light text-white">{currentQuestion.question}</h2>

            <div className="space-y-4">
              {currentQuestion.type === "text" ? (
                <Input
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full h-12 text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400"
                  autoFocus
                />
              ) : (
                <Textarea
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full min-h-[100px] text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400 resize-none"
                  autoFocus
                />
              )}

              <div className="flex gap-3">
                <Button
                  onClick={handleQuestionSubmit}
                  disabled={!questionAnswer.trim() && !currentQuestion.optional}
                  className="flex-1 h-12 bg-red-600 hover:bg-red-700 text-white rounded-none font-medium"
                >
                  Continue
                </Button>
                {currentQuestion.optional && (
                  <Button
                    onClick={() => {
                      setCurrentQuestion(null)
                      setTimeout(() => processNextTask(planningContext), 500)
                    }}
                    variant="outline"
                    className="h-12 px-6 border-gray-600 text-gray-300 hover:bg-gray-800 rounded-none"
                  >
                    Skip
                  </Button>
                )}
              </div>
            </div>

            {currentQuestion.optional && <p className="text-xs text-gray-500">This question is optional</p>}
          </motion.div>
        </div>
      </div>
    )
  }

  if (!hasStarted) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-2xl text-center space-y-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="flex items-center justify-center gap-3 mb-6">
              <Image
                src="/AG3NT.png"
                alt="AG3NT"
                width={120}
                height={40}
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Image Upload Preview */}
              {uploadedImages.length > 0 && (
                <div className="p-3 rounded-lg" style={{ backgroundColor: '#818181' }}>
                  {isProcessingImages && (
                    <div className="flex items-center gap-2 mb-3 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Analyzing images with AI...</span>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Upload ${index + 1}`}
                          className={`w-16 h-16 object-cover rounded border transition-opacity ${
                            isProcessingImages ? 'opacity-50' : 'opacity-100'
                          }`}
                          title={image.name}
                        />
                        {!isProcessingImages && (
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X size={12} />
                          </button>
                        )}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity truncate">
                          {image.name}
                        </div>
                      </div>
                    ))}
                  </div>
                  {uploadedImages.length > 0 && !isProcessingImages && (
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-gray-600">
                        {uploadedImages.length} image{uploadedImages.length > 1 ? 's' : ''} ready for AI analysis
                      </div>
                      <button
                        onClick={clearAllImages}
                        className="text-xs text-red-500 hover:text-red-700 underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Input with Paperclip and Send Button */}
              <div className="flex w-full items-center gap-0 rounded-full p-0 overflow-hidden" style={{padding:0}}>
                <Button
                  onClick={triggerImageUpload}
                  className="h-12 px-3 text-gray-600 bg-white border-0 rounded-l-full hover:bg-gray-50 transition-colors duration-200"
                  style={{ minWidth: 44 }}
                  title="Upload design reference images for AI analysis"
                  disabled={isProcessing || isProcessingImages}
                >
                  <Paperclip size={18} />
                  <span className="sr-only">Upload Images</span>
                </Button>
                <Input
                  value={userPrompt}
                  onChange={(e) => setUserPrompt(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="What would you like to build?"
                  className="flex-1 h-12 text-base text-black border-0 placeholder-gray-500 focus:ring-2 focus:ring-red-400 focus:ring-inset px-3 py-2"
                  autoFocus
                  style={{
                    paddingLeft: 12,
                    paddingRight: 8,
                    backgroundColor: '#ffffff',
                    color: '#000000'
                  }}
                />
                <Button
                  onClick={handleStartPlanning}
                  disabled={!userPrompt.trim()}
                  className="h-12 px-4 text-white rounded-r-full border-0 shadow-none hover:opacity-90 transition-opacity duration-200"
                  style={{
                    minWidth: 44,
                    backgroundColor: '#ff2d55'
                  }}
                >
                  <span className="sr-only">Start</span>
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                    <path d="M4 3v14l12-7L4 3z" fill="white"/>
                  </svg>
                </Button>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>

            {/* Mode Toggle */}
            <div className="flex items-center justify-center py-2">
  <div className="relative inline-block">
    <button
      className="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-900 text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-red-400 border border-gray-700"
      onClick={() => setIsInteractive((v) => !v)}
      aria-haspopup="listbox"
      aria-expanded="false"
    >
      Mode: {isInteractive ? "Interactive" : "Autonomous"} <svg className="ml-1 w-4 h-4" viewBox="0 0 20 20" fill="none"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
    </button>
    <div className="absolute left-0 mt-2 w-40 rounded-md shadow-lg bg-gray-900 border border-gray-800 z-10" style={{display:'none'}}>
      {/* Dropdown could be implemented here if needed */}
    </div>
  </div>
</div>

            <div className="text-center text-xs text-gray-400 mb-2">
  {isInteractive ? (
    <span>Guided Q&A for clarity.</span>
  ) : (
    <span>Autogenerates your project plan.</span>
  )}
</div>

            
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-gray-400 text-xs mt-2"
            style={{opacity:0.4}}
          >
            <span style={{ fontWeight: 'bold' }}>
              Powered by{' '}
              <span style={{ color: 'white' }}>AP3</span>
              <span style={{ color: '#ff2d55', textShadow: '0 0 8px #ff2d55, 0 0 16px #ff2d55' }}>X</span>
            </span>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black text-white p-4 flex flex-col relative">
      <div className="flex-grow flex items-center justify-center">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-4">
              AG3NT is Planning Your Project
            </h1>
            <div className="flex items-center justify-center gap-2 mt-2">
              {isInteractive ? (
                <>
                  <User className="w-4 h-4 text-red-400" />
                  <span className="text-xs text-red-400">Interactive Mode</span>
                </>
              ) : (
                <>
                  <Bot className="w-4 h-4 text-green-400" />
                  <span className="text-xs text-green-400">Autonomous Mode</span>
                </>
              )}
            </div>
            {error && (
              <div className="flex flex-col items-center justify-center gap-2 mt-2">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-400" />
                  <span className="text-xs text-yellow-400">{error}</span>
                </div>
                {canRetry && (
                  <Button
                    onClick={retryCurrentStep}
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-xs px-3 py-1 h-6"
                  >
                    Retry
                  </Button>
                )}
              </div>
            )}
          </motion.div>

          <motion.ul layout className="space-y-4 mb-8 w-full">
            <AnimatePresence initial={false}>
              {tasks.map((task, index) => {
                const isActive = index === currentTaskIndex
                const isCompleted = task.completed
                const isPending = index > currentTaskIndex

                return (
                  <motion.li
                    key={task.id}
                    layout
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 50 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30, duration: 0.3 }}
                    className={`py-3 cursor-pointer group ${isCompleted ? "opacity-60" : ""}`}
                  >
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="w-5 h-5 flex-shrink-0 relative"
                        animate={isActive ? { rotate: 360 } : { rotate: 0 }}
                        transition={
                          isActive ? { duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" } : { duration: 0 }
                        }
                      >
                        <svg
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-full h-full"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            stroke={isCompleted ? "#5e6ad2" : isPending ? "#4b5563" : "#ffffff"}
                            strokeWidth="2"
                            fill={isCompleted ? "#5e6ad2" : "none"}
                            className={isActive ? "opacity-30" : ""}
                          />
                          {isActive && (
                            <path
                              d="M12 2C13.3132 2 14.6136 2.25866 15.8268 2.7612C17.0401 3.26375 18.1425 4.00035 19.0711 4.92893C19.9997 5.85752 20.7362 6.95991 21.2388 8.17317C21.7413 9.38642 22 10.6868 22 12"
                              stroke="#ffffff"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          )}
                          {isCompleted && !isActive && (
                            <path
                              d="M8 12L11 15L16 9"
                              stroke="black"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          )}
                        </svg>
                      </motion.div>
                      <div className="flex-grow flex items-center justify-between overflow-hidden">
                        <span
                          className={`text-sm truncate transition-colors duration-300 ease-in-out ${
                            isCompleted
                              ? "text-gray-500"
                              : isActive
                                ? "text-white"
                                : isPending
                                  ? "text-gray-400"
                                  : "text-gray-300"
                          }`}
                        >
                          {task.id === "design" && uploadedImages.length > 0
                            ? "Analyze uploaded design references"
                            : task.title}
                        </span>
                        {task.id === "design" && uploadedImages.length > 0 && (
                          <div className="flex items-center gap-1 ml-2">
                            <svg className="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span className="text-xs text-blue-400">From Images</span>
                          </div>
                        )}
                        {task.id === "design" && task.completed && results.design && typeof results.design === "string" && results.design.includes("Style Guide") && (
                          <div className="mt-1 text-xs text-green-400">
                            ✓ Comprehensive style guide generated
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.li>
                )
              })}
            </AnimatePresence>
          </motion.ul>

          {!isProcessing && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center space-y-4"
            >
              <div className="text-green-400 font-semibold">✓ Planning Complete!</div>
              <Button
                onClick={openResults}
                className="w-full bg-red-600 hover:bg-red-700 flex items-center justify-center gap-2"
              >
                View AG3NT's Analysis
                <ExternalLink className="w-4 h-4" />
              </Button>
            </motion.div>
          )}

          <div className="flex items-center justify-center space-x-2 text-gray-500 mt-8">
            <span className="text-xs">Powered by</span>
            <Sparkles className="w-3 h-3" />
            <span className="text-xs font-bold">
              <span>
                <span style={{ color: 'white', fontWeight: 'bold' }}>AP3</span>
                <span style={{ color: '#ff3333', textShadow: '0 0 8px #ff3333', fontWeight: 'bold' }}>X</span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
