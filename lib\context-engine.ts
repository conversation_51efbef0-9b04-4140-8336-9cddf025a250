import type { ProjectContext } from "@/types/planning"

/**
 * Advanced Context Engineering System
 * Implements sophisticated context management and RAG techniques
 */
export class ContextEngine {
  private context: ProjectContext
  private memoryStore: Map<string, any> = new Map()
  // Removed ragSources - not used in current implementation

  constructor(initialContext: Partial<ProjectContext>) {
    this.context = {
      originalPrompt: "",
      projectType: "",
      features: [],
      clarifications: {},
      summary: "",
      techStack: {},
      prd: {},
      wireframes: [],
      filesystem: {},
      workflow: {},
      tasks: [],
      ...initialContext,
    }
  }

  /**
   * Context Engineering Technique: Memory Management
   * Maintains coherent context across all planning steps
   */
  updateContext(step: string, data: any): void {
    this.context = { ...this.context, [step]: data }
    this.memoryStore.set(`step_${step}`, {
      data,
      timestamp: new Date().toISOString(),
      dependencies: this.getStepDependencies(step),
    })
  }

  /**
   * Context Engineering Technique: Dependency Tracking
   * Ensures each step has access to relevant prior decisions
   */
  private getStepDependencies(step: string): string[] {
    const dependencies: Record<string, string[]> = {
      analyze: [],
      clarify: ["analyze"],
      summary: ["analyze", "clarify"],
      techstack: ["analyze", "clarify", "summary"],
      prd: ["analyze", "clarify", "summary", "techstack"],
      wireframes: ["summary", "prd"],
      filesystem: ["techstack", "prd"],
      workflow: ["analyze", "summary", "prd"],
      tasks: ["prd", "wireframes", "filesystem", "workflow"],
    }
    return dependencies[step] || []
  }

  /**
   * Context Engineering Technique: RAG Integration
   * Retrieves relevant external information for enhanced planning
   */
  async enhanceWithRAG(step: string, query: string): Promise<any> {
    // Real RAG integration would go here - for now, return structured context
    // This could integrate with vector databases, documentation, etc.

    return {
      context: this.getRelevantContext(step),
      externalKnowledge: {
        step,
        query,
        timestamp: new Date().toISOString(),
        note: "Real RAG integration would provide external knowledge here"
      },
      bestPractices: this.getBestPractices(step),
      templates: this.getTemplates(step),
    }
  }

  /**
   * Context Engineering Technique: Template Management
   * Provides consistent structures for document generation
   */
  private getTemplates(step: string): any {
    const templates: Record<string, any> = {
      prd: {
        sections: ["Purpose", "Features", "User Stories", "Technical Requirements", "Acceptance Criteria"],
        format: "structured_document",
      },
      summary: {
        sections: ["Overview", "Scope", "Goals", "Key Features"],
        format: "narrative",
      },
      tasks: {
        structure: ["Setup", "Frontend", "Backend", "Testing", "Deployment"],
        format: "categorized_list",
      },
    }
    return templates[step] || {}
  }

  /**
   * Context Engineering Technique: Contextual Retrieval
   * Gets relevant context for current planning step
   */
  getRelevantContext(step: string): any {
    const dependencies = this.getStepDependencies(step)
    const relevantContext: any = {}

    dependencies.forEach((dep) => {
      const stepData = this.memoryStore.get(`step_${dep}`)
      if (stepData) {
        relevantContext[dep] = stepData.data
      }
    })

    return {
      current: this.context,
      dependencies: relevantContext,
      metadata: {
        projectType: this.context.projectType,
        complexity: this.assessComplexity(),
        timestamp: new Date().toISOString(),
      },
    }
  }

  /**
   * Context Engineering Technique: Complexity Assessment
   * Dynamically assesses project complexity for better planning
   */
  private assessComplexity(): string {
    const factors = {
      features: this.context.features?.length || 0,
      integrations: Object.keys(this.context.clarifications || {}).length,
      techStackComplexity: Object.keys(this.context.techStack || {}).length,
    }

    const score = factors.features * 2 + factors.integrations + factors.techStackComplexity

    if (score < 5) return "Simple"
    if (score < 15) return "Medium"
    return "Complex"
  }

  /**
   * Context Engineering Technique: Best Practices Integration
   * Provides domain-specific best practices for each step
   */
  private getBestPractices(step: string): string[] {
    const practices: Record<string, string[]> = {
      techstack: [
        "Choose technologies with strong community support",
        "Consider long-term maintenance and scalability",
        "Align with team expertise and project timeline",
      ],
      prd: [
        "Write clear, testable acceptance criteria",
        "Include both functional and non-functional requirements",
        "Consider edge cases and error scenarios",
      ],
      filesystem: [
        "Follow framework conventions and best practices",
        "Separate concerns with clear folder structure",
        "Plan for scalability and maintainability",
      ],
    }
    return practices[step] || []
  }

  /**
   * Context Engineering Technique: Validation and Security
   * Validates context integrity and prevents injection attacks
   */
  validateContext(): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    // Validate required fields
    if (!this.context.originalPrompt?.trim()) {
      issues.push("Original prompt is required")
    }

    // Validate data integrity
    if (this.context.features && !Array.isArray(this.context.features)) {
      issues.push("Features must be an array")
    }

    // Security validation - prevent prompt injection
    const suspiciousPatterns = [/ignore\s+previous\s+instructions/i, /system\s*:/i, /\[INST\]/i]

    const textToCheck = JSON.stringify(this.context)
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(textToCheck)) {
        issues.push("Potentially malicious content detected")
        break
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
    }
  }

  /**
   * Context Engineering Technique: Context Serialization
   * Exports complete context for external systems
   */
  exportContext(): {
    context: ProjectContext
    metadata: any
    validation: any
  } {
    return {
      context: this.context,
      metadata: {
        complexity: this.assessComplexity(),
        completeness: this.calculateCompleteness(),
        timestamp: new Date().toISOString(),
        version: "1.0",
      },
      validation: this.validateContext(),
    }
  }

  /**
   * Context Engineering Technique: Completeness Assessment
   * Measures how complete the planning context is
   */
  private calculateCompleteness(): number {
    const requiredFields = ["originalPrompt", "projectType", "features", "summary", "techStack", "prd"]

    const completedFields = requiredFields.filter((field) => {
      const value = this.context[field as keyof ProjectContext]
      return value && (typeof value === "string" ? value.trim() : Object.keys(value).length > 0)
    })

    return Math.round((completedFields.length / requiredFields.length) * 100)
  }

  // Removed mock RAG retrieval - real RAG integration would go in enhanceWithRAG method
}
