import { type NextRequest, NextResponse } from "next/server"
import { aiService } from "@/lib/ai-service"

export async function POST(request: NextRequest) {
  try {
    const { prompt, isInteractive, answers = {}, designStyleGuide, hasImages } = await request.json()

    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 })
    }

    console.log("Starting AI-powered planning for:", prompt)
    if (hasImages) {
      console.log("Planning includes design style guide from uploaded images")
    }

    // Initialize planning with real AI analysis
    try {
      const analysis = await aiService.analyzePrompt(prompt, { designStyleGuide, hasImages })
      console.log("AI analysis completed:", analysis)

      const result = {
        success: true,
        prompt,
        isInteractive,
        answers,
        currentStep: "analyze",
        results: {
          analyze: analysis,
          ...(designStyleGuide && { design: designStyleGuide })
        },
        needsInput: false,
        completed: false,
        hasImages,
        designStyleGuide
      }

      return NextResponse.json(result)

    } catch (aiError) {
      console.error("AI analysis failed:", aiError)
      return NextResponse.json(
        {
          error: "AI analysis failed",
          details: aiError instanceof Error ? aiError.message : "Unknown AI error",
          suggestion: "Please check your OPENROUTER_API_KEY and try again"
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("Planning API error:", error)
    return NextResponse.json(
      {
        error: "Failed to process planning request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
